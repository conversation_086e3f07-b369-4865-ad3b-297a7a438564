{% load static %}
{% load i18n %}
{% load image_utils %}
<!DOCTYPE html>
<html lang="{{ current_language|default:'ar' }}" dir="{{ text_direction|default:'rtl' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="HR Management System - Comprehensive employee management solution">
    <title>{% block title %}نظام إدارة الموارد البشرية{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    {% if system_settings.font_family == 'cairo' or current_font == 'Cairo' %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'tajawal' or current_font == 'Tajawal' %}
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'almarai' or current_font == 'Almarai' %}
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@400;700&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'ibm-plex-sans-arabic' or current_font == 'IBM Plex Sans Arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;500;600&display=swap" rel="stylesheet">
    {% elif system_settings.font_family == 'noto-sans-arabic' or current_font == 'Noto Sans Arabic' %}
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {% else %}
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    {% endif %}

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2c3e50;
            --success-color: #2ecc71;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #3498db;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --font-family: {% if system_settings.font_family %}
                {% if system_settings.font_family == 'cairo' %}'Cairo'
                {% elif system_settings.font_family == 'tajawal' %}'Tajawal'
                {% elif system_settings.font_family == 'almarai' %}'Almarai'
                {% elif system_settings.font_family == 'ibm-plex-sans-arabic' %}'IBM Plex Sans Arabic'
                {% elif system_settings.font_family == 'noto-sans-arabic' %}'Noto Sans Arabic'
                {% else %}'Cairo'
                {% endif %}
                {% else %}{{ current_font|default:'Cairo' }}{% endif %}, sans-serif;
        }

        body {
            font-family: var(--font-family);
            background-color: #f8f9fa;
        }

        .navbar {
            background-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }

        .navbar-light .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.85);
        }

        .navbar-light .navbar-nav .nav-link:hover {
            color: white;
        }

        .with-sidebar {
            padding-left: 0;
            padding-right: 0;
        }

        .page-wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .content-wrapper {
            display: flex;
            flex: 1;
        }

        .sidebar {
            width: 250px;
            background-color: var(--secondary-color);
            color: white;
            padding-top: 20px;
            transition: all 0.3s;
            position: fixed;
            height: 100%;
            z-index: 100;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.5rem;
            color: white;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu-item {
            margin-bottom: 5px;
        }

        .sidebar-menu-item a {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s;
        }

        .sidebar-menu-item a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu-item a i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar-menu-item.active a {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            margin-right: 250px;
            transition: all 0.3s;
        }

        .sidebar-collapsed .sidebar {
            width: 70px;
        }

        .sidebar-collapsed .sidebar-header h3,
        .sidebar-collapsed .sidebar-menu-item a span {
            display: none;
        }

        .sidebar-collapsed .main-content {
            margin-right: 70px;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            border: none;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 10px 10px 0 0 !important;
            padding: 15px 20px;
        }

        .card-title {
            margin-bottom: 0;
            font-weight: bold;
            color: var(--secondary-color);
        }

        .card-body {
            padding: 20px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #27ae60;
            border-color: #27ae60;
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .btn-danger:hover {
            background-color: #c0392b;
            border-color: #c0392b;
        }

        .table th {
            font-weight: bold;
            background-color: #f8f9fa;
        }

        .stat-card {
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            color: white;
            position: relative;
            overflow: hidden;
            min-height: 120px;
        }

        .stat-card .stat-icon {
            position: absolute;
            bottom: -15px;
            left: 10px;
            font-size: 4rem;
            opacity: 0.3;
        }

        .stat-card .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-card .stat-title {
            font-size: 1rem;
            opacity: 0.8;
        }

        .stat-card.primary {
            background-color: var(--primary-color);
        }

        .stat-card.success {
            background-color: var(--success-color);
        }

        .stat-card.warning {
            background-color: var(--warning-color);
        }

        .stat-card.danger {
            background-color: var(--danger-color);
        }

        .stat-card.info {
            background-color: var(--info-color);
        }

        .stat-card.secondary {
            background-color: var(--secondary-color);
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .sidebar {
                width: 0;
                padding: 0;
                transform: translateX(100%);
                right: -250px;
                z-index: 1050;
            }

            .main-content {
                margin-right: 0;
            }

            .sidebar.show {
                width: 250px;
                padding-top: 20px;
                transform: translateX(0);
                right: 0;
            }

            /* Overlay for mobile sidebar */
            .sidebar-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1040;
            }

            .sidebar-overlay.show {
                display: block;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="page-wrapper">
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg navbar-light with-sidebar">
            <div class="container-fluid">
                <div class="d-flex align-items-center">
                    <button id="sidebarToggle" class="btn btn-light me-2">
                        <i class="fas fa-bars"></i>
                    </button>
                    <a class="navbar-brand" href="{% url 'Hr:dashboard' %}">
                        <i class="fas fa-users me-2"></i>
                        <span>نظام الموارد البشرية</span>
                    </a>
                </div>
                <div class="d-flex align-items-center">
                    <div class="user-info me-3 d-flex align-items-center">
                        <i class="fas fa-user-circle text-light me-2 fs-5"></i>
                        <span class="text-light fw-bold">{{ request.user.username }}</span>
                    </div>
                    <a href="{% url 'accounts:logout' %}" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i> تسجيل الخروج
                    </a>
                </div>
            </div>
        </nav>

        <div class="content-wrapper">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3><i class="fas fa-users me-2"></i> الموارد البشرية</h3>
                </div>
                <ul class="sidebar-menu">
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                        <a href="{% url 'Hr:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'list' and 'employees' in request.path %}active{% endif %}">
                        <a href="{% url 'Hr:employees:list' %}">
                            <i class="fas fa-users"></i>
                            <span>قائمة الموظفين</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'create' and 'employees' in request.path %}active{% endif %}">
                        <a href="{% url 'Hr:employees:create' %}">
                            <i class="fas fa-user-plus"></i>
                            <span>إضافة موظف جديد</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'employee_search' %}active{% endif %}">
                        <a href="{% url 'Hr:employees:employee_search' %}">
                            <i class="fas fa-search"></i>
                            <span>البحث عن موظف</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'list' and 'departments' in request.path %}active{% endif %}">
                        <a href="{% url 'Hr:departments:list' %}">
                            <i class="fas fa-building"></i>
                            <span>الأقسام</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'list' and 'jobs' in request.path %}active{% endif %}">
                        <a href="{% url 'Hr:jobs:list' %}">
                            <i class="fas fa-briefcase"></i>
                            <span>الوظائف</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'salary_item_list' %}active{% endif %}">
                        <a href="{% url 'Hr:salary_item_list' %}">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>الرواتب</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if 'attendance/dashboard' in request.path %}active{% endif %}">
                        <a href="{% url 'attendance:dashboard' %}">
                            <i class="fas fa-clock"></i>
                            <span>نظام الحضور والانصراف</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'record_list' %}active{% endif %}">
                        <a href="{% url 'attendance:record_list' %}">
                            <i class="fas fa-clipboard-check"></i>
                            <span>سجل الحضور والانصراف</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'mark_attendance' %}active{% endif %}">
                        <a href="{% url 'attendance:mark_attendance' %}">
                            <i class="fas fa-fingerprint"></i>
                            <span>تسجيل الحضور</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item {% if request.resolver_match.url_name == 'list' and 'alerts' in request.path %}active{% endif %}">
                        <a href="{% url 'Hr:alerts:list' %}">
                            <i class="fas fa-bell"></i>
                            <span>التنبيهات</span>
                        </a>
                    </li>
                    <li class="sidebar-menu-item">
                        <a href="{% url 'accounts:home' %}">
                            <i class="fas fa-home"></i>
                            <span>الصفحة الرئيسية</span>
                        </a>
                    </li>
                </ul>
            </aside>

            <!-- Main Content -->
            <div class="main-content">
                {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}

                <!-- Page Header -->
                <div class="page-header mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1">{% block page_title %}الموارد البشرية{% endblock %}</h2>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0">
                                    {% block breadcrumb %}
                                    <li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
                                    <li class="breadcrumb-item active">الموارد البشرية</li>
                                    {% endblock %}
                                </ol>
                            </nav>
                        </div>
                        <div class="page-actions">
                            {% block page_actions %}{% endblock %}
                        </div>
                    </div>
                </div>

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar Toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const pageWrapper = document.querySelector('.page-wrapper');
            const sidebar = document.querySelector('.sidebar');

            // Create overlay element for mobile
            const overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    pageWrapper.classList.toggle('sidebar-collapsed');

                    // For mobile devices
                    if (window.innerWidth <= 768) {
                        sidebar.classList.toggle('show');
                        overlay.classList.toggle('show');
                    }
                });
            }

            // Close sidebar when clicking on overlay
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
                pageWrapper.classList.remove('sidebar-collapsed');
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    overlay.classList.remove('show');
                }
            });

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
